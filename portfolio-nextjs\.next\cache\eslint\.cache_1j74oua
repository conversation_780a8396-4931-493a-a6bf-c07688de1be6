[{"C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\layout.tsx": "1", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx": "2", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Footer.tsx": "3", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Navigation.tsx": "4", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\AboutSection.tsx": "5", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ContactSection.tsx": "6", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ExperienceSection.tsx": "7", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\HeroSection.tsx": "8", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ProjectsSection.tsx": "9", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ServicesSection.tsx": "10", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\SkillsSection.tsx": "11", "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\StatsSection.tsx": "12"}, {"size": 1048, "mtime": 1749526084284, "results": "13", "hashOfConfig": "14"}, {"size": 1041, "mtime": 1749526084284, "results": "15", "hashOfConfig": "14"}, {"size": 7936, "mtime": 1749526084286, "results": "16", "hashOfConfig": "14"}, {"size": 4074, "mtime": 1749526084286, "results": "17", "hashOfConfig": "14"}, {"size": 5786, "mtime": 1749526084288, "results": "18", "hashOfConfig": "14"}, {"size": 6144, "mtime": 1749526084290, "results": "19", "hashOfConfig": "14"}, {"size": 6092, "mtime": 1749526084292, "results": "20", "hashOfConfig": "14"}, {"size": 5961, "mtime": 1749527043219, "results": "21", "hashOfConfig": "14"}, {"size": 6919, "mtime": 1749526084294, "results": "22", "hashOfConfig": "14"}, {"size": 5270, "mtime": 1749526084294, "results": "23", "hashOfConfig": "14"}, {"size": 4970, "mtime": 1749526084296, "results": "24", "hashOfConfig": "14"}, {"size": 5091, "mtime": 1749526084296, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ecp3ba", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\layout.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\app\\page.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Footer.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\AboutSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ContactSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ExperienceSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ProjectsSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\ServicesSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\SkillsSection.tsx", [], [], "C:\\laragon\\www\\rizqibennington.github.io\\portfolio-nextjs\\src\\components\\sections\\StatsSection.tsx", ["62"], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 20, "column": 9, "nodeType": "65", "endLine": 41, "endColumn": 4}, "react-hooks/exhaustive-deps", "The 'stats' array makes the dependencies of useEffect Hook (at line 62) change on every render. To fix this, wrap the initialization of 'stats' in its own useMemo() Hook.", "VariableDeclarator"]