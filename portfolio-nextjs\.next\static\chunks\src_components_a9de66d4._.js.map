{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Menu, X } from 'lucide-react';\r\n\r\nconst Navigation = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsScrolled(window.scrollY > 150);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  const navItems = [\r\n    { href: '#home-section', label: 'Home' },\r\n    { href: '#about-section', label: 'About' },\r\n    { href: '#resume-section', label: 'Experience' },\r\n    { href: '#services-section', label: 'Services' },\r\n    { href: '#skills-section', label: 'Skills' },\r\n    { href: '#projects-section', label: 'Projects' },\r\n    { href: '#contact-section', label: 'Contact' },\r\n  ];\r\n\r\n  const scrollToSection = (href: string) => {\r\n    const element = document.querySelector(href);\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth', block: 'start' });\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.nav\r\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\r\n        isScrolled \r\n          ? 'bg-gray-900/95 backdrop-blur-sm shadow-lg' \r\n          : 'bg-transparent'\r\n      }`}\r\n      initial={{ y: -100 }}\r\n      animate={{ y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"flex items-center justify-between h-16\">\r\n          {/* Logo */}\r\n          <motion.a\r\n            href=\"#home-section\"\r\n            className=\"text-2xl font-bold text-white hover:text-blue-400 transition-colors\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              scrollToSection('#home-section');\r\n            }}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n          >\r\n            RR\r\n          </motion.a>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex space-x-8\">\r\n            {navItems.map((item, index) => (\r\n              <motion.a\r\n                key={item.href}\r\n                href={item.href}\r\n                className=\"text-white hover:text-blue-400 transition-colors font-medium\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  scrollToSection(item.href);\r\n                }}\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                whileHover={{ scale: 1.05 }}\r\n              >\r\n                {item.label}\r\n              </motion.a>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Mobile Menu Button */}\r\n          <button\r\n            className=\"md:hidden text-white p-2\"\r\n            onClick={() => setIsOpen(!isOpen)}\r\n            aria-label=\"Toggle menu\"\r\n          >\r\n            {isOpen ? <X size={24} /> : <Menu size={24} />}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Mobile Navigation */}\r\n        <motion.div\r\n          className={`md:hidden ${isOpen ? 'block' : 'hidden'}`}\r\n          initial={{ opacity: 0, height: 0 }}\r\n          animate={{ \r\n            opacity: isOpen ? 1 : 0, \r\n            height: isOpen ? 'auto' : 0 \r\n          }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          <div className=\"py-4 space-y-2\">\r\n            {navItems.map((item) => (\r\n              <a\r\n                key={item.href}\r\n                href={item.href}\r\n                className=\"block text-white hover:text-blue-400 transition-colors py-2 px-4 rounded\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  scrollToSection(item.href);\r\n                }}\r\n              >\r\n                {item.label}\r\n              </a>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </motion.nav>\r\n  );\r\n};\r\n\r\nexport default Navigation;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAiB,OAAO;QAAO;QACvC;YAAE,MAAM;YAAkB,OAAO;QAAQ;QACzC;YAAE,MAAM;YAAmB,OAAO;QAAa;QAC/C;YAAE,MAAM;YAAqB,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAmB,OAAO;QAAS;QAC3C;YAAE,MAAM;YAAqB,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAoB,OAAO;QAAU;KAC9C;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;gBAAU,OAAO;YAAQ;YAC5D,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,4DAA4D,EACtE,aACI,8CACA,kBACJ;QACF,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,MAAK;4BACL,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,gBAAgB;4BAClB;4BACA,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCACzB;;;;;;sCAKD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,gBAAgB,KAAK,IAAI;oCAC3B;oCACA,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,YAAY;wCAAE,OAAO;oCAAK;8CAEzB,KAAK,KAAK;mCAZN,KAAK,IAAI;;;;;;;;;;sCAkBpB,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,cAAW;sCAEV,uBAAS,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAK5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,UAAU,EAAE,SAAS,UAAU,UAAU;oBACrD,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBACP,SAAS,SAAS,IAAI;wBACtB,QAAQ,SAAS,SAAS;oBAC5B;oBACA,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB,KAAK,IAAI;gCAC3B;0CAEC,KAAK,KAAK;+BARN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB9B;GAtHM;KAAA;uCAwHS", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  Twitter, \r\n  Facebook, \r\n  Instagram, \r\n  ArrowRight, \r\n  MessageCircle, \r\n  Mail,\r\n  Heart\r\n} from 'lucide-react';\r\n\r\nconst Footer = () => {\r\n  const currentYear = new Date().getFullYear();\r\n\r\n  const socialLinks = [\r\n    {\r\n      icon: Twitter,\r\n      href: 'https://twitter.com/rizqibennington',\r\n      label: 'Twitter'\r\n    },\r\n    {\r\n      icon: Facebook,\r\n      href: 'https://facebook.com/rizqirahmansyah10',\r\n      label: 'Facebook'\r\n    },\r\n    {\r\n      icon: Instagram,\r\n      href: 'https://instagram.com/rizqibennington',\r\n      label: 'Instagram'\r\n    }\r\n  ];\r\n\r\n  const quickLinks = [\r\n    { href: '#home-section', label: 'Home' },\r\n    { href: '#about-section', label: 'About' },\r\n    { href: '#services-section', label: 'Services' },\r\n    { href: '#projects-section', label: 'Projects' },\r\n    { href: '#contact-section', label: 'Contact' }\r\n  ];\r\n\r\n  const services = [\r\n    'Web Design',\r\n    'Web Development',\r\n    'Business Strategy',\r\n    'Data Analysis',\r\n    'Graphic Design'\r\n  ];\r\n\r\n  const scrollToSection = (href: string) => {\r\n    const element = document.querySelector(href);\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth', block: 'start' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <footer className=\"bg-gray-900 text-white\">\r\n      <div className=\"container mx-auto px-4 py-16\">\r\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {/* About Section */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h3 className=\"text-xl font-semibold mb-4\">About</h3>\r\n            <p className=\"text-gray-400 mb-6 leading-relaxed\">\r\n              Want to connect with me? feel free to follow my social media\r\n            </p>\r\n            \r\n            <div className=\"flex space-x-4\">\r\n              {socialLinks.map((social, index) => {\r\n                const IconComponent = social.icon;\r\n                return (\r\n                  <motion.a\r\n                    key={social.label}\r\n                    href={social.href}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-colors\"\r\n                    whileHover={{ scale: 1.1, y: -2 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    whileInView={{ opacity: 1, y: 0 }}\r\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                    viewport={{ once: true }}\r\n                  >\r\n                    <IconComponent size={18} />\r\n                  </motion.a>\r\n                );\r\n              })}\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Quick Links */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h3 className=\"text-xl font-semibold mb-4\">Links</h3>\r\n            <ul className=\"space-y-3\">\r\n              {quickLinks.map((link, index) => (\r\n                <motion.li\r\n                  key={link.href}\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  whileInView={{ opacity: 1, x: 0 }}\r\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                >\r\n                  <a\r\n                    href={link.href}\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      scrollToSection(link.href);\r\n                    }}\r\n                    className=\"text-gray-400 hover:text-white transition-colors flex items-center group\"\r\n                  >\r\n                    <ArrowRight size={16} className=\"mr-2 group-hover:translate-x-1 transition-transform\" />\r\n                    {link.label}\r\n                  </a>\r\n                </motion.li>\r\n              ))}\r\n            </ul>\r\n          </motion.div>\r\n\r\n          {/* Services */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.4 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h3 className=\"text-xl font-semibold mb-4\">Services</h3>\r\n            <ul className=\"space-y-3\">\r\n              {services.map((service, index) => (\r\n                <motion.li\r\n                  key={service}\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  whileInView={{ opacity: 1, x: 0 }}\r\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                >\r\n                  <a\r\n                    href=\"#services-section\"\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      scrollToSection('#services-section');\r\n                    }}\r\n                    className=\"text-gray-400 hover:text-white transition-colors flex items-center group\"\r\n                  >\r\n                    <ArrowRight size={16} className=\"mr-2 group-hover:translate-x-1 transition-transform\" />\r\n                    {service}\r\n                  </a>\r\n                </motion.li>\r\n              ))}\r\n            </ul>\r\n          </motion.div>\r\n\r\n          {/* Contact Info */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.6 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <h3 className=\"text-xl font-semibold mb-4\">Have a Questions?</h3>\r\n            <div className=\"space-y-4\">\r\n              <motion.a\r\n                href=\"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-center text-gray-400 hover:text-white transition-colors group\"\r\n                whileHover={{ x: 5 }}\r\n              >\r\n                <MessageCircle size={20} className=\"mr-3 text-green-500\" />\r\n                <span>+6281293062103</span>\r\n              </motion.a>\r\n              \r\n              <motion.a\r\n                href=\"mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help\"\r\n                className=\"flex items-center text-gray-400 hover:text-white transition-colors group\"\r\n                whileHover={{ x: 5 }}\r\n              >\r\n                <Mail size={20} className=\"mr-3 text-blue-500\" />\r\n                <span><EMAIL></span>\r\n              </motion.a>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <motion.div\r\n          className=\"border-t border-gray-800 mt-12 pt-8 text-center\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          transition={{ duration: 0.6, delay: 0.8 }}\r\n          viewport={{ once: true }}\r\n        >\r\n          <p className=\"text-gray-400\">\r\n            Copyright &copy; {currentYear} All rights reserved | This template is made with{' '}\r\n            <Heart size={16} className=\"inline text-red-500 mx-1\" /> by{' '}\r\n            <a \r\n              href=\"https://colorlib.com\" \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-blue-400 hover:text-blue-300 transition-colors\"\r\n            >\r\n              Colorlib\r\n            </a>\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAaA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,+MAAA,CAAA,YAAS;YACf,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAiB,OAAO;QAAO;QACvC;YAAE,MAAM;YAAkB,OAAO;QAAQ;QACzC;YAAE,MAAM;YAAqB,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAqB,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAoB,OAAO;QAAU;KAC9C;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;gBAAU,OAAO;YAAQ;QAC9D;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;wCACxB,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,OAAO;gDAAK,GAAG,CAAC;4CAAE;4CAChC,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;sDAEvB,cAAA,6LAAC;gDAAc,MAAM;;;;;;2CAZhB,OAAO,KAAK;;;;;oCAevB;;;;;;;;;;;;sCAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;sDAEvB,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,gBAAgB,KAAK,IAAI;gDAC3B;gDACA,WAAU;;kEAEV,6LAAC,qNAAA,CAAA,aAAU;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC/B,KAAK,KAAK;;;;;;;2CAfR,KAAK,IAAI;;;;;;;;;;;;;;;;sCAuBtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;sDAEvB,cAAA,6LAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,gBAAgB;gDAClB;gDACA,WAAU;;kEAEV,6LAAC,qNAAA,CAAA,aAAU;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC/B;;;;;;;2CAfE;;;;;;;;;;;;;;;;sCAuBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAE;;8DAEnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DACnC,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAE;;8DAEnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC1B,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAE,WAAU;;4BAAgB;4BACT;4BAAY;4BAAkD;0CAChF,6LAAC,uMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA6B;4BAAI;0CAC5D,6LAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA9MM;uCAgNS", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport Image from 'next/image';\r\n\r\nconst HeroSection = () => {\r\n  const scrollToSection = (href: string) => {\r\n    const element = document.querySelector(href);\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth', block: 'start' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section\r\n      id=\"home-section\"\r\n      className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800 pt-16\"\r\n    >\r\n      {/* Background overlay */}\r\n      <div className=\"absolute inset-0 bg-black/30\"></div>\r\n      \r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <div className=\"grid md:grid-cols-2 gap-8 items-center\">\r\n          {/* Text Content */}\r\n          <motion.div\r\n            className=\"text-white space-y-6\"\r\n            initial={{ opacity: 0, x: -50 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n          >\r\n            <motion.span\r\n              className=\"text-lg text-blue-400 font-medium\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n            >\r\n              Hello!\r\n            </motion.span>\r\n            \r\n            <motion.h1\r\n              className=\"text-4xl md:text-6xl font-bold leading-tight\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.4 }}\r\n            >\r\n              I&apos;m <span className=\"text-blue-400\">Rizqi Rahmansyah</span>\r\n            </motion.h1>\r\n            \r\n            <motion.h2\r\n              className=\"text-2xl md:text-3xl text-gray-300 font-light\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n            >\r\n              A Web Developer Enthusiast\r\n            </motion.h2>\r\n            \r\n            <motion.div\r\n              className=\"flex flex-col sm:flex-row gap-4 pt-4\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.8 }}\r\n            >\r\n              <motion.a\r\n                href=\"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center\"\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                Hire me\r\n              </motion.a>\r\n              \r\n              <motion.button\r\n                onClick={() => scrollToSection('#about-section')}\r\n                className=\"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-medium transition-all inline-flex items-center justify-center\"\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                About Me\r\n              </motion.button>\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          {/* Image */}\r\n          <motion.div\r\n            className=\"relative\"\r\n            initial={{ opacity: 0, x: 50 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.3 }}\r\n          >\r\n            <div className=\"relative w-full max-w-md mx-auto\">\r\n              <motion.div\r\n                className=\"relative z-10\"\r\n                whileHover={{ scale: 1.05 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <Image\r\n                  src=\"/images/me2.png\"\r\n                  alt=\"Rizqi Rahmansyah\"\r\n                  width={400}\r\n                  height={500}\r\n                  className=\"w-full h-auto rounded-lg shadow-2xl\"\r\n                  priority\r\n                />\r\n              </motion.div>\r\n              \r\n              {/* Decorative elements */}\r\n              <motion.div\r\n                className=\"absolute -top-4 -right-4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl\"\r\n                animate={{ \r\n                  scale: [1, 1.2, 1],\r\n                  opacity: [0.3, 0.6, 0.3]\r\n                }}\r\n                transition={{ \r\n                  duration: 3,\r\n                  repeat: Infinity,\r\n                  ease: \"easeInOut\"\r\n                }}\r\n              />\r\n              \r\n              <motion.div\r\n                className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl\"\r\n                animate={{ \r\n                  scale: [1.2, 1, 1.2],\r\n                  opacity: [0.4, 0.2, 0.4]\r\n                }}\r\n                transition={{ \r\n                  duration: 4,\r\n                  repeat: Infinity,\r\n                  ease: \"easeInOut\"\r\n                }}\r\n              />\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Scroll indicator */}\r\n      <motion.div\r\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, delay: 1.2 }}\r\n      >\r\n        <motion.div\r\n          className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\r\n          animate={{ y: [0, 10, 0] }}\r\n          transition={{ duration: 2, repeat: Infinity }}\r\n        >\r\n          <motion.div\r\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\r\n            animate={{ opacity: [1, 0, 1] }}\r\n            transition={{ duration: 2, repeat: Infinity }}\r\n          />\r\n        </motion.div>\r\n      </motion.div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default HeroSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,cAAc;IAClB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;gBAAU,OAAO;YAAQ;QAC9D;IACF;IAEA,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;wCACzC;sDACU,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAG3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDACzB;;;;;;sDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDACzB;;;;;;;;;;;;;;;;;;sCAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAKZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAC1B;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;kDAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAK;gDAAG;6CAAI;4CACpB,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAC1B;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAE5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;wBAAC;wBAC9B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;;;;;;;AAMxD;KA3JM;uCA6JS", "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/AboutSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport Image from 'next/image';\r\nimport { useEffect, useState } from 'react';\r\n\r\nconst AboutSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const [workingDays, setWorkingDays] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (inView) {\r\n      // Animate counter from 0 to 730\r\n      let start = 0;\r\n      const end = 730;\r\n      const duration = 2000; // 2 seconds\r\n      const increment = end / (duration / 16); // 60fps\r\n\r\n      const timer = setInterval(() => {\r\n        start += increment;\r\n        if (start >= end) {\r\n          setWorkingDays(end);\r\n          clearInterval(timer);\r\n        } else {\r\n          setWorkingDays(Math.floor(start));\r\n        }\r\n      }, 16);\r\n\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [inView]);\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"about-section\" className=\"py-20 bg-gray-50\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        <motion.div\r\n          className=\"grid md:grid-cols-2 gap-12 items-center\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {/* Image */}\r\n          <motion.div\r\n            className=\"relative\"\r\n            variants={itemVariants}\r\n          >\r\n            <div className=\"relative w-full max-w-md mx-auto\">\r\n              <motion.div\r\n                whileHover={{ scale: 1.02 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <Image\r\n                  src=\"/images/aboutme.png\"\r\n                  alt=\"About Rizqi Rahmansyah\"\r\n                  width={400}\r\n                  height={500}\r\n                  className=\"w-full h-auto rounded-lg shadow-xl\"\r\n                />\r\n              </motion.div>\r\n              \r\n              {/* Decorative overlay */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-t from-blue-600/20 to-transparent rounded-lg\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Content */}\r\n          <motion.div\r\n            className=\"space-y-6\"\r\n            variants={itemVariants}\r\n          >\r\n            <div>\r\n              <motion.h1\r\n                className=\"text-6xl font-bold text-gray-200 mb-2\"\r\n                variants={itemVariants}\r\n              >\r\n                About\r\n              </motion.h1>\r\n              <motion.h2\r\n                className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\"\r\n                variants={itemVariants}\r\n              >\r\n                About Me\r\n              </motion.h2>\r\n            </div>\r\n\r\n            <motion.p\r\n              className=\"text-gray-600 leading-relaxed text-lg\"\r\n              variants={itemVariants}\r\n            >\r\n              My journey into web development started with a fascination for how the web works,\r\n              leading me to master technologies like HTML, CSS, JavaScript, and frameworks such as\r\n              React and Angular on the front end. On the server side, I&apos;m skilled in PHP, Node.js,\r\n              and MySQL, with a particular focus on building scalable and secure web applications.\r\n            </motion.p>\r\n\r\n            <motion.ul\r\n              className=\"space-y-3\"\r\n              variants={containerVariants}\r\n            >\r\n              {[\r\n                { label: 'Name:', value: 'Rizqi Rahmansyah' },\r\n                { label: 'Date of birth:', value: 'December 10, 1998' },\r\n                { label: 'Address:', value: 'Bekasi' },\r\n                { label: 'Phone:', value: '+6281293062103' },\r\n              ].map((item, index) => (\r\n                <motion.li\r\n                  key={index}\r\n                  className=\"flex text-gray-700\"\r\n                  variants={itemVariants}\r\n                >\r\n                  <span className=\"font-semibold w-32\">{item.label}</span>\r\n                  <span>{item.value}</span>\r\n                </motion.li>\r\n              ))}\r\n            </motion.ul>\r\n\r\n            {/* Counter */}\r\n            <motion.div\r\n              className=\"bg-white p-6 rounded-lg shadow-lg\"\r\n              variants={itemVariants}\r\n            >\r\n              <div className=\"text-center\">\r\n                <motion.div\r\n                  className=\"text-4xl font-bold text-blue-600 mb-2\"\r\n                  initial={{ scale: 0 }}\r\n                  animate={inView ? { scale: 1 } : { scale: 0 }}\r\n                  transition={{ duration: 0.5, delay: 0.5 }}\r\n                >\r\n                  {workingDays}\r\n                </motion.div>\r\n                <div className=\"text-gray-600 font-medium\">Days Working Experience</div>\r\n              </div>\r\n              \r\n              <motion.div\r\n                className=\"mt-6 flex justify-center\"\r\n                variants={itemVariants}\r\n              >\r\n                <motion.a\r\n                  href=\"#\"\r\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block\"\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  Download CV\r\n                </motion.a>\r\n              </motion.div>\r\n            </motion.div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AboutSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,eAAe;;IACnB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,gCAAgC;gBAChC,IAAI,QAAQ;gBACZ,MAAM,MAAM;gBACZ,MAAM,WAAW,MAAM,YAAY;gBACnC,MAAM,YAAY,MAAM,CAAC,WAAW,EAAE,GAAG,QAAQ;gBAEjD,MAAM,QAAQ;oDAAY;wBACxB,SAAS;wBACT,IAAI,SAAS,KAAK;4BAChB,eAAe;4BACf,cAAc;wBAChB,OAAO;4BACL,eAAe,KAAK,KAAK,CAAC;wBAC5B;oBACF;mDAAG;gBAEH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAgB,WAAU;QAAmB,KAAK;kBAC5D,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,SAAS,YAAY;;kCAG9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;;0CAEV,6LAAC;;kDACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,UAAU;kDACX;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,UAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,UAAU;0CACX;;;;;;0CAOD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,UAAU;0CAET;oCACC;wCAAE,OAAO;wCAAS,OAAO;oCAAmB;oCAC5C;wCAAE,OAAO;wCAAkB,OAAO;oCAAoB;oCACtD;wCAAE,OAAO;wCAAY,OAAO;oCAAS;oCACrC;wCAAE,OAAO;wCAAU,OAAO;oCAAiB;iCAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,WAAU;wCACV,UAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAsB,KAAK,KAAK;;;;;;0DAChD,6LAAC;0DAAM,KAAK,KAAK;;;;;;;uCALZ;;;;;;;;;;0CAWX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS,SAAS;oDAAE,OAAO;gDAAE,IAAI;oDAAE,OAAO;gDAAE;gDAC5C,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DAEvC;;;;;;0DAEH,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;;;;;;;kDAG7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;kDAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAzKM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCA2KS", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/ExperienceSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\n\r\nconst ExperienceSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const experiences = [\r\n    {\r\n      period: \"2014 - 2017\",\r\n      title: \"SMAN 2 Tambun Selatan\",\r\n      position: \"Highschool of Science\",\r\n      description: \"The first experience when I'm falling in love with IT Things.\"\r\n    },\r\n    {\r\n      period: \"2017 - 2021\",\r\n      title: \"University of Singaperbangsa Karawang\",\r\n      position: \"Bachelor's Degree of Computer Science\",\r\n      description: \"My first step to begin my journey to be a developer. I started my coding journey here and graduated with a cum laude predicate with a GPA of 3.71.\"\r\n    },\r\n    {\r\n      period: \"January - June (2020)\",\r\n      title: \"PT Telekomunikasi Indonesia (WITEL Karawang)\",\r\n      position: \"Chatbot Developer\",\r\n      description: \"My first working experience as an intern. Here, I developed a chatbot on Telegram to analyze, monitor, and count the jobs already done by technicians.\"\r\n    },\r\n    {\r\n      period: \"January 2022 - February 2023\",\r\n      title: \"Topkarir Indonesia\",\r\n      position: \"Fullstack Web Developer\",\r\n      description: \"My first Working Experience in the professional field was in Topkarir, a startup company in Human Resource field, in this place my responsibility is Develop and Maintenance Website, Frontend, Backend, API and connect to third party API for Topkarir Website and Microsite there are Jaknaker.id, Jatimcerdas, jabarjawara, TopKarir, AUA Topkarir, CDC BPSDMI. Topkarir. often using MySQL and PostgreSQL ,PHP, Javascript, Jquery, CSS, HTML and GO. often using framework Codeigniter and Laravel\"\r\n    },\r\n    {\r\n      period: \"March 2023 - March 2024\",\r\n      title: \"Katadata Indonesia\",\r\n      position: \"Web Developer\",\r\n      description: \"Responsible for developing, maintaining, and testing the Katadata website, API, and client-side applications using frameworks like Laravel, CodeIgniter, and React JS, with programming languages such as PHP and JavaScript. Also involved in data scraping using Python to support the data engineering team. Katadata is a startup in the online media industry.\"\r\n    },\r\n    {\r\n      period: \"July 2024 - Now\",\r\n      title: \"RS Nasional Kanker Dharmais\",\r\n      position: \"Programmer\",\r\n      description: \"Developed and maintained hospital management software, optimizing workflows and enhancing patient data management. Collaborated with medical staff to create custom applications for scheduling, electronic medical records (EMR), and reporting tools. Ensured data security and compliance with health regulations by implementing robust cybersecurity measures. Improved system performance and user experience through continuous code optimization and feature updates.\"\r\n    }\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, x: -30 },\r\n    visible: {\r\n      opacity: 1,\r\n      x: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"resume-section\" className=\"py-20 bg-white\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Header */}\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h1 className=\"text-6xl font-bold text-gray-200 mb-2\">Life Experience</h1>\r\n          <h2 className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\">Life Experience</h2>\r\n          <p className=\"text-gray-600 mt-4 text-lg\">Experience is the most valuable lesson from God</p>\r\n        </motion.div>\r\n\r\n        {/* Timeline */}\r\n        <motion.div\r\n          className=\"grid md:grid-cols-2 gap-8\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {experiences.map((exp, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"relative\"\r\n              variants={itemVariants}\r\n            >\r\n              <motion.div\r\n                className=\"bg-gray-50 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300\"\r\n                whileHover={{ y: -5 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <motion.span\r\n                  className=\"inline-block bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium mb-4\"\r\n                  initial={{ scale: 0 }}\r\n                  animate={inView ? { scale: 1 } : { scale: 0 }}\r\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                >\r\n                  {exp.period}\r\n                </motion.span>\r\n                \r\n                <h3 className=\"text-xl font-bold text-gray-800 mb-2\">{exp.title}</h3>\r\n                <h4 className=\"text-blue-600 font-semibold mb-3\">{exp.position}</h4>\r\n                <p className=\"text-gray-600 leading-relaxed\">{exp.description}</p>\r\n              </motion.div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Download CV Button */}\r\n        <motion.div\r\n          className=\"text-center mt-12\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6, delay: 0.8 }}\r\n        >\r\n          <motion.a\r\n            href=\"#\"\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block\"\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n          >\r\n            Download CV\r\n          </motion.a>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ExperienceSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,oBAAoB;;IACxB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,cAAc;QAClB;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,UAAU;YACV,aAAa;QACf;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAiB,WAAU;QAAiB,KAAK;kBAC3D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;8BAE7B,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS,SAAS;4CAAE,OAAO;wCAAE,IAAI;4CAAE,OAAO;wCAAE;wCAC5C,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAE/C,IAAI,MAAM;;;;;;kDAGb,6LAAC;wCAAG,WAAU;kDAAwC,IAAI,KAAK;;;;;;kDAC/D,6LAAC;wCAAG,WAAU;kDAAoC,IAAI,QAAQ;;;;;;kDAC9D,6LAAC;wCAAE,WAAU;kDAAiC,IAAI,WAAW;;;;;;;;;;;;2BApB1D;;;;;;;;;;8BA2BX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;;;;;AAOX;GArIM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAuIS", "debugId": null}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/ServicesSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport { \r\n  Code, \r\n  Camera, \r\n  Lightbulb, \r\n  BarChart3, \r\n  Palette, \r\n  Target \r\n} from 'lucide-react';\r\n\r\nconst ServicesSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const services = [\r\n    {\r\n      icon: Code,\r\n      title: 'Web Design',\r\n      description: 'Creating beautiful and functional web designs that engage users and drive results.'\r\n    },\r\n    {\r\n      icon: Camera,\r\n      title: 'Photography',\r\n      description: 'Professional photography services for events, portraits, and commercial projects.'\r\n    },\r\n    {\r\n      icon: Lightbulb,\r\n      title: 'Web Developer',\r\n      description: 'Full-stack web development using modern technologies and best practices.'\r\n    },\r\n    {\r\n      icon: BarChart3,\r\n      title: 'App Developing',\r\n      description: 'Mobile and web application development with focus on user experience.'\r\n    },\r\n    {\r\n      icon: Palette,\r\n      title: 'Branding',\r\n      description: 'Complete branding solutions including logo design and brand identity.'\r\n    },\r\n    {\r\n      icon: Target,\r\n      title: 'Product Strategy',\r\n      description: 'Strategic planning and consultation for digital product development.'\r\n    }\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"services-section\" className=\"py-20 bg-white\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Header */}\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h1 className=\"text-6xl font-bold text-gray-200 mb-2\">Services</h1>\r\n          <h2 className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\">Services</h2>\r\n          <p className=\"text-gray-600 mt-4 text-lg max-w-2xl mx-auto\">\r\n            Far far away, behind the word mountains, far from the countries Vokalia and Consonantia\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Services Grid */}\r\n        <motion.div\r\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {services.map((service) => {\r\n            const IconComponent = service.icon;\r\n            \r\n            return (\r\n              <motion.div\r\n                key={service.title}\r\n                className=\"group\"\r\n                variants={itemVariants}\r\n              >\r\n                <motion.div\r\n                  className=\"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg text-center transition-all duration-300 cursor-pointer h-full\"\r\n                  whileHover={{ \r\n                    y: -10,\r\n                    boxShadow: \"0 20px 40px rgba(0,0,0,0.1)\"\r\n                  }}\r\n                  transition={{ duration: 0.3 }}\r\n                >\r\n                  <motion.div\r\n                    className=\"inline-flex items-center justify-center w-16 h-16 bg-blue-100 group-hover:bg-blue-200 rounded-full mb-6 transition-colors duration-300\"\r\n                    whileHover={{ scale: 1.1, rotate: 5 }}\r\n                    transition={{ duration: 0.3 }}\r\n                  >\r\n                    <IconComponent \r\n                      size={32} \r\n                      className=\"text-blue-600 group-hover:text-blue-700 transition-colors duration-300\" \r\n                    />\r\n                  </motion.div>\r\n                  \r\n                  <h3 className=\"text-xl font-semibold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors duration-300\">\r\n                    {service.title}\r\n                  </h3>\r\n                  \r\n                  <p className=\"text-gray-600 leading-relaxed\">\r\n                    {service.description}\r\n                  </p>\r\n                </motion.div>\r\n              </motion.div>\r\n            );\r\n          })}\r\n        </motion.div>\r\n\r\n        {/* Call to Action */}\r\n        <motion.div\r\n          className=\"text-center mt-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6, delay: 1 }}\r\n        >\r\n          <motion.a\r\n            href=\"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block\"\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n          >\r\n            Get Started\r\n          </motion.a>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ServicesSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAaA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,+MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAmB,WAAU;QAAiB,KAAK;kBAC7D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAM9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;8BAE7B,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,gBAAgB,QAAQ,IAAI;wBAElC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCACV,GAAG,CAAC;oCACJ,WAAW;gCACb;gCACA,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAK,QAAQ;wCAAE;wCACpC,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;4CACC,MAAM;4CACN,WAAU;;;;;;;;;;;kDAId,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAGhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;;;;;;;2BA5BnB,QAAQ,KAAK;;;;;oBAiCxB;;;;;;8BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;8BAEtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;;;;;AAOX;GAhJM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAkJS", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/SkillsSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\n\r\nconst SkillsSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const skills = [\r\n    { name: 'CSS', percentage: 90, color: 'bg-blue-500' },\r\n    { name: 'jQuery', percentage: 85, color: 'bg-green-500' },\r\n    { name: 'HTML5', percentage: 95, color: 'bg-orange-500' },\r\n    { name: 'PHP', percentage: 90, color: 'bg-purple-500' },\r\n    { name: 'Javascript', percentage: 70, color: 'bg-yellow-500' },\r\n    { name: 'Python', percentage: 80, color: 'bg-red-500' },\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"skills-section\" className=\"py-20 bg-gray-50\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Header */}\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h1 className=\"text-6xl font-bold text-gray-200 mb-2\">Skills</h1>\r\n          <h2 className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\">My Skills</h2>\r\n          <p className=\"text-gray-600 mt-4 text-lg\">here&apos;s some my major skill</p>\r\n        </motion.div>\r\n\r\n        {/* Skills Grid */}\r\n        <motion.div\r\n          className=\"grid md:grid-cols-2 gap-8\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {skills.map((skill, index) => (\r\n            <motion.div\r\n              key={skill.name}\r\n              className=\"bg-white p-6 rounded-lg shadow-lg\"\r\n              variants={itemVariants}\r\n              whileHover={{ y: -5 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{skill.name}</h3>\r\n                <span className=\"text-sm font-medium text-gray-600\">{skill.percentage}%</span>\r\n              </div>\r\n              \r\n              <div className=\"w-full bg-gray-200 rounded-full h-3 overflow-hidden\">\r\n                <motion.div\r\n                  className={`h-full ${skill.color} rounded-full relative`}\r\n                  initial={{ width: 0 }}\r\n                  animate={inView ? { width: `${skill.percentage}%` } : { width: 0 }}\r\n                  transition={{ \r\n                    duration: 1.5, \r\n                    delay: index * 0.2,\r\n                    ease: \"easeOut\"\r\n                  }}\r\n                >\r\n                  {/* Animated shine effect */}\r\n                  <motion.div\r\n                    className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\"\r\n                    initial={{ x: '-100%' }}\r\n                    animate={inView ? { x: '100%' } : { x: '-100%' }}\r\n                    transition={{ \r\n                      duration: 1.5, \r\n                      delay: index * 0.2 + 0.5,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Additional Skills Tags */}\r\n        <motion.div\r\n          className=\"mt-16 text-center\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6, delay: 1 }}\r\n        >\r\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-6\">Other Technologies</h3>\r\n          <div className=\"flex flex-wrap justify-center gap-3\">\r\n            {[\r\n              'React', 'Angular', 'Node.js', 'MySQL', 'PostgreSQL', \r\n              'Laravel', 'CodeIgniter', 'Git', 'Docker', 'AWS'\r\n            ].map((tech, index) => (\r\n              <motion.span\r\n                key={tech}\r\n                className=\"bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\"\r\n                initial={{ opacity: 0, scale: 0 }}\r\n                animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}\r\n                transition={{ \r\n                  duration: 0.3, \r\n                  delay: 1.2 + index * 0.1,\r\n                  ease: \"easeOut\"\r\n                }}\r\n                whileHover={{ scale: 1.1 }}\r\n              >\r\n                {tech}\r\n              </motion.span>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default SkillsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,SAAS;QACb;YAAE,MAAM;YAAO,YAAY;YAAI,OAAO;QAAc;QACpD;YAAE,MAAM;YAAU,YAAY;YAAI,OAAO;QAAe;QACxD;YAAE,MAAM;YAAS,YAAY;YAAI,OAAO;QAAgB;QACxD;YAAE,MAAM;YAAO,YAAY;YAAI,OAAO;QAAgB;QACtD;YAAE,MAAM;YAAc,YAAY;YAAI,OAAO;QAAgB;QAC7D;YAAE,MAAM;YAAU,YAAY;YAAI,OAAO;QAAa;KACvD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAiB,WAAU;QAAmB,KAAK;kBAC7D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;8BAE7B,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;4BAAE;4BACpB,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC,MAAM,IAAI;;;;;;sDAC/D,6LAAC;4CAAK,WAAU;;gDAAqC,MAAM,UAAU;gDAAC;;;;;;;;;;;;;8CAGxE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,sBAAsB,CAAC;wCACxD,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS,SAAS;4CAAE,OAAO,GAAG,MAAM,UAAU,CAAC,CAAC,CAAC;wCAAC,IAAI;4CAAE,OAAO;wCAAE;wCACjE,YAAY;4CACV,UAAU;4CACV,OAAO,QAAQ;4CACf,MAAM;wCACR;kDAGA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;4CAAQ;4CACtB,SAAS,SAAS;gDAAE,GAAG;4CAAO,IAAI;gDAAE,GAAG;4CAAQ;4CAC/C,YAAY;gDACV,UAAU;gDACV,OAAO,QAAQ,MAAM;gDACrB,MAAM;4CACR;;;;;;;;;;;;;;;;;2BA/BD,MAAM,IAAI;;;;;;;;;;8BAwCrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;;sCAEtC,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;gCAAS;gCAAW;gCAAW;gCAAS;gCACxC;gCAAW;gCAAe;gCAAO;gCAAU;6BAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,SAAS,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpE,YAAY;wCACV,UAAU;wCACV,OAAO,MAAM,QAAQ;wCACrB,MAAM;oCACR;oCACA,YAAY;wCAAE,OAAO;oCAAI;8CAExB;mCAXI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBrB;GAlIM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAoIS", "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/ProjectsSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport Image from 'next/image';\r\nimport { ExternalLink } from 'lucide-react';\r\n\r\nconst ProjectsSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const projects = [\r\n    {\r\n      title: 'Katadata ESG Index',\r\n      subtitle: 'Katadata Green',\r\n      image: '/images/katadata-esg-index.png',\r\n      description: 'Environmental, Social, and Governance index platform for sustainable business evaluation.',\r\n      link: '#'\r\n    },\r\n    {\r\n      title: 'Sistem Informasi Komite Etik dan Hukum Dharmais',\r\n      subtitle: 'SIKETIK',\r\n      image: '/images/siketik.png',\r\n      description: 'Ethics and Legal Committee Information System for hospital management.',\r\n      link: '#'\r\n    },\r\n    {\r\n      title: 'Indeks <PERSON>',\r\n      subtitle: 'Databoks Katadata',\r\n      image: '/images/marketdata.png',\r\n      description: 'Stock market index tracking and analysis platform.',\r\n      link: '#'\r\n    },\r\n    {\r\n      title: '<PERSON><PERSON><PERSON> Sentimen Debat Pilpres 2024',\r\n      subtitle: 'Katadata Pemilu 2024',\r\n      image: '/images/analisis-sentimen.png',\r\n      description: 'Sentiment analysis platform for 2024 presidential election debates.',\r\n      link: '#'\r\n    },\r\n    {\r\n      title: 'Career Development Center BPSDMI',\r\n      subtitle: 'TopKarir x Kemenperin',\r\n      image: '/images/bpsdmi.png',\r\n      description: 'Career development platform for industrial workforce.',\r\n      link: '#'\r\n    },\r\n    {\r\n      title: 'Jabar Jawara Career Center',\r\n      subtitle: 'Disnakertrans Jabar X TopKarir',\r\n      image: '/images/jabarjawara.png',\r\n      description: 'West Java career center platform for job seekers and employers.',\r\n      link: '#'\r\n    }\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"projects-section\" className=\"py-20 bg-gray-50\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Header */}\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h1 className=\"text-6xl font-bold text-gray-200 mb-2\">Projects</h1>\r\n          <h2 className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\">My Projects</h2>\r\n          <p className=\"text-gray-600 mt-4 text-lg\">Here&apos;s Some of My Project that already done</p>\r\n        </motion.div>\r\n\r\n        {/* Projects Grid */}\r\n        <motion.div\r\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {projects.map((project) => (\r\n            <motion.div\r\n              key={project.title}\r\n              className=\"group relative overflow-hidden rounded-lg shadow-lg bg-white\"\r\n              variants={itemVariants}\r\n              whileHover={{ y: -5 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <div className=\"relative h-64 overflow-hidden\">\r\n                <Image\r\n                  src={project.image}\r\n                  alt={project.title}\r\n                  fill\r\n                  className=\"object-cover transition-transform duration-300 group-hover:scale-110\"\r\n                />\r\n                \r\n                {/* Overlay */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\r\n                  <div className=\"absolute bottom-4 left-4 right-4 text-white\">\r\n                    <h3 className=\"text-lg font-semibold mb-1 line-clamp-2\">\r\n                      {project.title}\r\n                    </h3>\r\n                    <p className=\"text-sm text-gray-300 mb-3\">\r\n                      {project.subtitle}\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-200 line-clamp-2 mb-3\">\r\n                      {project.description}\r\n                    </p>\r\n                    \r\n                    <motion.a\r\n                      href={project.link}\r\n                      className=\"inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      View Project\r\n                      <ExternalLink size={16} />\r\n                    </motion.a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* Content (visible on mobile) */}\r\n              <div className=\"p-6 md:hidden\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\r\n                  {project.title}\r\n                </h3>\r\n                <p className=\"text-sm text-blue-600 mb-2\">\r\n                  {project.subtitle}\r\n                </p>\r\n                <p className=\"text-gray-600 text-sm mb-4\">\r\n                  {project.description}\r\n                </p>\r\n                <a\r\n                  href={project.link}\r\n                  className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm\"\r\n                >\r\n                  View Project\r\n                  <ExternalLink size={16} />\r\n                </a>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* View More Button */}\r\n        <motion.div\r\n          className=\"text-center mt-12\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6, delay: 1 }}\r\n        >\r\n          <motion.a\r\n            href=\"#contact-section\"\r\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block\"\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              document.querySelector('#contact-section')?.scrollIntoView({ \r\n                behavior: 'smooth', \r\n                block: 'start' \r\n              });\r\n            }}\r\n          >\r\n            Let&apos;s Work Together\r\n          </motion.a>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProjectsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAmB,WAAU;QAAmB,KAAK;kBAC/D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;8BAE7B,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;4BAAE;4BACpB,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,KAAK;4CAClB,IAAI;4CACJ,WAAU;;;;;;sDAIZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,QAAQ;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAGtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAM,QAAQ,IAAI;wDAClB,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;4DACzB;0EAEC,6LAAC,yNAAA,CAAA,eAAY;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,QAAQ;;;;;;sDAEnB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC;4CACC,MAAM,QAAQ,IAAI;4CAClB,WAAU;;gDACX;8DAEC,6LAAC,yNAAA,CAAA,eAAY;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;2BAxDnB,QAAQ,KAAK;;;;;;;;;;8BAgExB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;8BAEtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,CAAC;4BACR,EAAE,cAAc;4BAChB,SAAS,aAAa,CAAC,qBAAqB,eAAe;gCACzD,UAAU;gCACV,OAAO;4BACT;wBACF;kCACD;;;;;;;;;;;;;;;;;;;;;;AAOX;GAvLM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAyLS", "debugId": null}}, {"offset": {"line": 2721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/StatsSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport { useEffect, useState } from 'react';\r\n\r\nconst StatsSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const [counters, setCounters] = useState({\r\n    certifications: 0,\r\n    experience: 0,\r\n    projects: 0,\r\n    industries: 0,\r\n  });\r\n\r\n  const stats = [\r\n    {\r\n      key: 'certifications',\r\n      target: 3,\r\n      label: 'Certification',\r\n    },\r\n    {\r\n      key: 'experience',\r\n      target: 2,\r\n      label: 'Years Working Experience',\r\n    },\r\n    {\r\n      key: 'projects',\r\n      target: 50,\r\n      label: 'Projects Completed',\r\n    },\r\n    {\r\n      key: 'industries',\r\n      target: 4,\r\n      label: 'Industry Fields',\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (inView) {\r\n      stats.forEach((stat) => {\r\n        let start = 0;\r\n        const end = stat.target;\r\n        const duration = 2000; // 2 seconds\r\n        const increment = end / (duration / 16); // 60fps\r\n\r\n        const timer = setInterval(() => {\r\n          start += increment;\r\n          if (start >= end) {\r\n            setCounters(prev => ({ ...prev, [stat.key]: end }));\r\n            clearInterval(timer);\r\n          } else {\r\n            setCounters(prev => ({ ...prev, [stat.key]: Math.floor(start) }));\r\n          }\r\n        }, 16);\r\n      });\r\n    }\r\n  }, [inView, stats]);\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-20 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden\" ref={ref}>\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-10\">\r\n        <div className=\"absolute inset-0\" style={{\r\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\r\n        }} />\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <motion.div\r\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {stats.map((stat, index) => (\r\n            <motion.div\r\n              key={stat.key}\r\n              className=\"text-center text-white\"\r\n              variants={itemVariants}\r\n            >\r\n              <motion.div\r\n                className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300\"\r\n                whileHover={{ \r\n                  scale: 1.05,\r\n                  boxShadow: \"0 10px 30px rgba(255,255,255,0.1)\"\r\n                }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <motion.div\r\n                  className=\"text-4xl md:text-5xl font-bold mb-2\"\r\n                  initial={{ scale: 0 }}\r\n                  animate={inView ? { scale: 1 } : { scale: 0 }}\r\n                  transition={{ \r\n                    duration: 0.5, \r\n                    delay: index * 0.2,\r\n                    type: \"spring\",\r\n                    stiffness: 100\r\n                  }}\r\n                >\r\n                  {counters[stat.key as keyof typeof counters]}\r\n                </motion.div>\r\n                \r\n                <motion.div\r\n                  className=\"text-blue-100 font-medium text-sm md:text-base\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n                  transition={{ duration: 0.6, delay: index * 0.2 + 0.3 }}\r\n                >\r\n                  {stat.label}\r\n                </motion.div>\r\n              </motion.div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Additional decorative elements */}\r\n        <motion.div\r\n          className=\"absolute top-10 left-10 w-20 h-20 bg-white/5 rounded-full blur-xl\"\r\n          animate={{ \r\n            scale: [1, 1.2, 1],\r\n            opacity: [0.3, 0.6, 0.3]\r\n          }}\r\n          transition={{ \r\n            duration: 4,\r\n            repeat: Infinity,\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n        \r\n        <motion.div\r\n          className=\"absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-xl\"\r\n          animate={{ \r\n            scale: [1.2, 1, 1.2],\r\n            opacity: [0.4, 0.2, 0.4]\r\n          }}\r\n          transition={{ \r\n            duration: 5,\r\n            repeat: Infinity,\r\n            ease: \"easeInOut\"\r\n          }}\r\n        />\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default StatsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,eAAe;;IACnB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,YAAY;IACd;IAEA,MAAM,QAAQ;QACZ;YACE,KAAK;YACL,QAAQ;YACR,OAAO;QACT;QACA;YACE,KAAK;YACL,QAAQ;YACR,OAAO;QACT;QACA;YACE,KAAK;YACL,QAAQ;YACR,OAAO;QACT;QACA;YACE,KAAK;YACL,QAAQ;YACR,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,MAAM,OAAO;8CAAC,CAAC;wBACb,IAAI,QAAQ;wBACZ,MAAM,MAAM,KAAK,MAAM;wBACvB,MAAM,WAAW,MAAM,YAAY;wBACnC,MAAM,YAAY,MAAM,CAAC,WAAW,EAAE,GAAG,QAAQ;wBAEjD,MAAM,QAAQ;4DAAY;gCACxB,SAAS;gCACT,IAAI,SAAS,KAAK;oCAChB;wEAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,CAAC,KAAK,GAAG,CAAC,EAAE;4CAAI,CAAC;;oCACjD,cAAc;gCAChB,OAAO;oCACL;wEAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,KAAK,CAAC;4CAAO,CAAC;;gCACjE;4BACF;2DAAG;oBACL;;YACF;QACF;iCAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAA4E,KAAK;;0BAElG,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,SAAS,SAAS,YAAY;kCAE7B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,UAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCACV,OAAO;wCACP,WAAW;oCACb;oCACA,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS,SAAS;gDAAE,OAAO;4CAAE,IAAI;gDAAE,OAAO;4CAAE;4CAC5C,YAAY;gDACV,UAAU;gDACV,OAAO,QAAQ;gDACf,MAAM;gDACN,WAAW;4CACb;sDAEC,QAAQ,CAAC,KAAK,GAAG,CAA0B;;;;;;sDAG9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7D,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ,MAAM;4CAAI;sDAErD,KAAK,KAAK;;;;;;;;;;;;+BAhCV,KAAK,GAAG;;;;;;;;;;kCAwCnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;;;;;;;AAKV;GAlKM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAoKS", "debugId": null}}, {"offset": {"line": 3012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/components/sections/ContactSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport { MapPin, MessageCircle, Mail } from 'lucide-react';\r\n\r\nconst ContactSection = () => {\r\n  const [ref, inView] = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.1,\r\n  });\r\n\r\n  const contactInfo = [\r\n    {\r\n      icon: MapPin,\r\n      title: 'Address',\r\n      content: 'Bekasi, West Java 17520',\r\n      link: null\r\n    },\r\n    {\r\n      icon: MessageCircle,\r\n      title: 'Contact Number',\r\n      content: '+6281293062103',\r\n      link: 'https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help'\r\n    },\r\n    {\r\n      icon: Mail,\r\n      title: 'Email Address',\r\n      content: '<EMAIL>',\r\n      link: 'mailto:<EMAIL>?subject=Need%20Help&body=Hi%20Rizqi,%20i%20need%20your%20help'\r\n    }\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.6 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section id=\"contact-section\" className=\"py-20 bg-white\" ref={ref}>\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Header */}\r\n        <motion.div\r\n          className=\"text-center mb-16\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h1 className=\"text-6xl font-bold text-gray-200 mb-2\">Contact</h1>\r\n          <h2 className=\"text-3xl font-bold text-gray-800 -mt-8 relative z-10\">Contact Me</h2>\r\n          <p className=\"text-gray-600 mt-4 text-lg\">Need some help with your website? feel free to contact me! :)</p>\r\n        </motion.div>\r\n\r\n        {/* Contact Info Cards */}\r\n        <motion.div\r\n          className=\"grid md:grid-cols-3 gap-8 mb-16\"\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate={inView ? \"visible\" : \"hidden\"}\r\n        >\r\n          {contactInfo.map((info) => {\r\n            const IconComponent = info.icon;\r\n            \r\n            return (\r\n              <motion.div\r\n                key={info.title}\r\n                className=\"text-center\"\r\n                variants={itemVariants}\r\n              >\r\n                <motion.div\r\n                  className=\"bg-gray-50 hover:bg-blue-50 p-8 rounded-lg transition-all duration-300\"\r\n                  whileHover={{ y: -5, boxShadow: \"0 10px 30px rgba(0,0,0,0.1)\" }}\r\n                  transition={{ duration: 0.3 }}\r\n                >\r\n                  <motion.div\r\n                    className=\"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6\"\r\n                    whileHover={{ scale: 1.1, rotate: 5 }}\r\n                    transition={{ duration: 0.3 }}\r\n                  >\r\n                    <IconComponent size={32} className=\"text-blue-600\" />\r\n                  </motion.div>\r\n                  \r\n                  <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">\r\n                    {info.title}\r\n                  </h3>\r\n                  \r\n                  {info.link ? (\r\n                    <motion.a\r\n                      href={info.link}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      className=\"text-blue-600 hover:text-blue-700 transition-colors\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                    >\r\n                      {info.content}\r\n                    </motion.a>\r\n                  ) : (\r\n                    <p className=\"text-gray-600\">{info.content}</p>\r\n                  )}\r\n                </motion.div>\r\n              </motion.div>\r\n            );\r\n          })}\r\n        </motion.div>\r\n\r\n        {/* Hire Me Section */}\r\n        <motion.div\r\n          className=\"relative bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg overflow-hidden\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n          transition={{ duration: 0.6, delay: 0.8 }}\r\n        >\r\n          {/* Background Image */}\r\n          <div \r\n            className=\"absolute inset-0 bg-cover bg-center opacity-20\"\r\n            style={{ backgroundImage: 'url(/images/bg_1.jpg)' }}\r\n          />\r\n          \r\n          <div className=\"relative z-10 text-center py-16 px-8\">\r\n            <motion.h2\r\n              className=\"text-3xl md:text-4xl font-bold text-white mb-4\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n              transition={{ duration: 0.6, delay: 1 }}\r\n            >\r\n              I&apos;m <span className=\"text-yellow-300\">Available</span> for freelancing\r\n            </motion.h2>\r\n            \r\n            <motion.p\r\n              className=\"text-xl text-blue-100 mb-8\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n              transition={{ duration: 0.6, delay: 1.2 }}\r\n            >\r\n              Feel Free to contact me\r\n            </motion.p>\r\n            \r\n            <motion.a\r\n              href=\"https://wa.me/6281293062103?text=Hi%20Rizqi,%20i%20need%20your%20help\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-medium text-lg transition-colors inline-block\"\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}\r\n              transition={{ duration: 0.6, delay: 1.4 }}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n            >\r\n              Contact Me Now\r\n            </motion.a>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ContactSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,SAAS;YACT,MAAM;QACR;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAkB,WAAU;QAAiB,KAAK;kBAC5D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;8BAE7B,YAAY,GAAG,CAAC,CAAC;wBAChB,MAAM,gBAAgB,KAAK,IAAI;wBAE/B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG,CAAC;oCAAG,WAAW;gCAA8B;gCAC9D,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAK,QAAQ;wCAAE;wCACpC,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;4CAAc,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAGrC,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;oCAGZ,KAAK,IAAI,iBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAM,KAAK,IAAI;wCACf,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;kDAEzB,KAAK,OAAO;;;;;6DAGf,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,OAAO;;;;;;;;;;;;2BAhCzC,KAAK,KAAK;;;;;oBAqCrB;;;;;;8BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAGxC,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAAwB;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAE;;wCACvC;sDACU,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;wCAAgB;;;;;;;8CAG7D,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACtE,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnKM;;QACkB,sKAAA,CAAA,YAAS;;;KAD3B;uCAqKS", "debugId": null}}]}