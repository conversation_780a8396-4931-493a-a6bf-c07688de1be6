{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_97fbdf92.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_97fbdf92-module___WkROa__className\",\n  \"variable\": \"poppins_97fbdf92-module___WkROa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_97fbdf92.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22],%22subsets%22:[%22latin%22],%22variable%22:%22--font-poppins%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\r\nimport { Poppins } from \"next/font/google\";\r\nimport \"./globals.css\";\r\n\r\nconst poppins = Poppins({\r\n  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],\r\n  subsets: [\"latin\"],\r\n  variable: \"--font-poppins\",\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Riz<PERSON>ah - Web Portfolio\",\r\n  description: \"Professional web developer portfolio showcasing skills in HTML, CSS, JavaScript, React, PHP, and more.\",\r\n  keywords: \"web developer, portfolio, React, JavaScript, PHP, HTML, CSS\",\r\n  authors: [{ name: \"<PERSON><PERSON><PERSON>\" }],\r\n  viewport: \"width=device-width, initial-scale=1, shrink-to-fit=no\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <link rel=\"icon\" href=\"/images/aboutme.png\" type=\"image/x-icon\" />\r\n      </head>\r\n      <body className={`${poppins.variable} font-poppins antialiased`}>\r\n        {children}\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAUO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;0BACC,cAAA,8OAAC;oBAAK,KAAI;oBAAO,MAAK;oBAAsB,MAAK;;;;;;;;;;;0BAEnD,8OAAC;gBAAK,WAAW,GAAG,2IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAC;0BAC5D;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/laragon/www/rizqibennington.github.io/portfolio-nextjs/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}