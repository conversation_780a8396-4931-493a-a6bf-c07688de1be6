# Rizqi Rahmansyah - Modern Portfolio

A modern, responsive portfolio website built with Next.js, TypeScript, and Tailwind CSS. This is a complete conversion from the original static HTML portfolio to a modern JavaScript framework while maintaining the same visual design and content.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15, TypeScript, and Tailwind CSS
- **Responsive Design**: Fully responsive across all devices
- **Smooth Animations**: Powered by Framer Motion for engaging user experience
- **Performance Optimized**: Static site generation for fast loading
- **SEO Friendly**: Optimized for search engines
- **GitHub Pages Ready**: Configured for automatic deployment

## 🛠️ Technologies Used

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Deployment**: GitHub Pages with GitHub Actions

## 📱 Sections

- **Hero**: Introduction with call-to-action
- **About**: Personal information and experience counter
- **Experience**: Professional timeline and education
- **Services**: Offered services with interactive cards
- **Skills**: Animated progress bars for technical skills
- **Projects**: Portfolio showcase with project details
- **Statistics**: Animated counters for achievements
- **Contact**: Contact information and hire me section

## 🚀 Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/rizqibennington/rizqibennington.github.io.git
cd portfolio-nextjs
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📦 Build and Deploy

### Local Build
```bash
npm run build
```

### Deploy to GitHub Pages
The project is configured with GitHub Actions for automatic deployment. Simply push to the main branch and the site will be automatically built and deployed.

## 🎨 Customization

### Colors
The color scheme can be customized in `src/app/globals.css`:
```css
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --dark-color: #343a40;
  --light-color: #f8f9fa;
  --orange-color: #fd7e14;
}
```

### Content
Update the content in the respective component files:
- Personal info: `src/components/sections/AboutSection.tsx`
- Experience: `src/components/sections/ExperienceSection.tsx`
- Projects: `src/components/sections/ProjectsSection.tsx`
- Skills: `src/components/sections/SkillsSection.tsx`

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome!

## 📞 Contact

- **Email**: <EMAIL>
- **WhatsApp**: +6281293062103
- **Website**: https://rizqibennington.github.io

---

Built with ❤️ by Rizqi Rahmansyah
