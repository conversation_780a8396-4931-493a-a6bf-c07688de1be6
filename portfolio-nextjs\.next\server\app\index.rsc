1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[9891,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
5:I[6132,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
6:I[1911,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
7:I[9112,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
8:I[180,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
9:I[5030,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
a:I[3286,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
b:I[8261,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
c:I[7602,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
d:I[2548,["566","static/chunks/566-56451db899fb20f8.js","974","static/chunks/app/page-33a86b6016d52b8f.js"],"default"]
e:I[9665,[],"MetadataBoundary"]
10:I[9665,[],"OutletBoundary"]
13:I[4911,[],"AsyncMetadataOutlet"]
15:I[9665,[],"ViewportBoundary"]
17:I[6614,[],""]
:HL["/_next/static/css/33c640af2ed62f4b.css","style"]
0:{"P":null,"b":"OIz7pCnB4IU1XZU1sGsRs","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/33c640af2ed62f4b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":["$","link",null,{"rel":"icon","href":"/images/aboutme.png","type":"image/x-icon"}]}],["$","body",null,{"className":"__variable_9b9fd1 font-poppins antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen","children":[["$","$L4",null,{}],["$","main",null,{"children":[["$","$L5",null,{}],["$","$L6",null,{}],["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","$La",null,{}],["$","$Lb",null,{}],["$","$Lc",null,{}]]}],["$","$Ld",null,{}]]}],["$","$Le",null,{"children":"$Lf"}],null,["$","$L10",null,{"children":["$L11","$L12",["$","$L13",null,{"promise":"$@14"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","D56Pq8YJtVQlHU1DkiVc9",{"children":[["$","$L15",null,{"children":"$L16"}],null]}],null]}],false]],"m":"$undefined","G":["$17","$undefined"],"s":false,"S":true}
18:"$Sreact.suspense"
19:I[4911,[],"AsyncMetadata"]
f:["$","$18",null,{"fallback":null,"children":["$","$L19",null,{"promise":"$@1a"}]}]
12:null
16:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
11:null
1a:{"metadata":[["$","title","0",{"children":"Rizqi Rahmansyah - Web Portfolio"}],["$","meta","1",{"name":"description","content":"Professional web developer portfolio showcasing skills in HTML, CSS, JavaScript, React, PHP, and more."}],["$","meta","2",{"name":"author","content":"Rizqi Rahmansyah"}],["$","meta","3",{"name":"keywords","content":"web developer, portfolio, React, JavaScript, PHP, HTML, CSS"}],["$","link","4",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
14:{"metadata":"$1a:metadata","error":null,"digest":"$undefined"}
